name: meditatingleo_admin
description: "MeditatingLeo Admin Panel - Desktop-optimized administrative interface for content management and system administration."
publish_to: 'none'
version: 0.1.0

environment:
  sdk: ^3.5.3

dependencies:
  flutter:
    sdk: flutter

  # State Management (Riverpod with code generation)
  flutter_riverpod: ^2.6.1
  riverpod_annotation: ^2.6.1

  # Database (Drift for local-first architecture)
  drift: ^2.26.1
  sqlite3_flutter_libs: ^0.5.24
  path_provider: ^2.1.4

  # Backend Integration (Supabase)
  supabase_flutter: ^2.8.0
  http: ^1.2.2

  # Navigation
  go_router: ^15.1.2

  # UI/UX Dependencies
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.4.1
  google_fonts: ^6.2.1

  # Data Handling (Freezed for immutable models)
  freezed_annotation: ^2.4.4
  json_annotation: ^4.9.0

  # Utilities
  intl: ^0.20.2
  uuid: ^4.5.1
  equatable: ^2.0.5

  # Admin-Specific Dependencies
  file_picker: ^10.1.9
  csv: ^6.0.0
  data_table_2: ^2.5.15
  flutter_quill: ^11.4.1

  # Analytics and Monitoring
  sentry_flutter: ^8.9.0
  package_info_plus: ^8.0.2

  # Authentication and Security
  crypto: ^3.0.5
  local_auth: ^2.3.0
  flutter_secure_storage: ^9.2.2

  # Environment Variables
  flutter_dotenv: ^5.2.1



dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  yaml: ^3.1.2

  # Code Generation
  build_runner: ^2.4.13
  riverpod_generator: ^2.6.2
  freezed: ^2.5.7
  json_serializable: ^6.8.0
  drift_dev: ^2.26.1

  # Testing
  mockito: ^5.4.4

flutter:
  uses-material-design: true

  assets:
    - .env
